const admin = require("firebase-admin");
const functions = require("firebase-functions");

// Lazy load SendGrid to avoid startup timeout
let sgMail = null;
let isInitialized = false;

const initializeSendGrid = () => {
  if (isInitialized) return true;

  try {
    const apiKey = functions.config().sendgrid?.api_key;
    if (!apiKey) {
      console.warn("SendGrid API key not found in Firebase config");
      return false;
    }

    // Extra lazy load SendGrid - only require when actually needed
    if (!sgMail) {
      try {
        sgMail = require("@sendgrid/mail");
      } catch (requireError) {
        console.error("Failed to require SendGrid:", requireError);
        return false;
      }
    }

    sgMail.setApiKey(apiKey);
    isInitialized = true;
    console.log("SendGrid initialized successfully");
    return true;
  } catch (error) {
    console.error("Error initializing SendGrid:", error);
    return false;
  }
};

// Email configuration from Firebase config
const getEmailConfig = () => {
  const config = functions.config();
  return {
    fromEmail: config.email?.from_email || "<EMAIL>",
    fromName: config.email?.from_name || "SkinGlow",
    replyTo: config.email?.reply_to || "<EMAIL>",
    supportEmail: config.email?.support_email || "<EMAIL>",
  };
};

// Beautiful email template
const getEmailTemplate = (content, title, type = "general") => {
  const config = getEmailConfig();

  const headerColors = {
    general: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    order: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
    support: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
    welcome: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
  };

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background: #f8f9fa; 
            margin: 0; 
            padding: 20px; 
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 16px; 
            overflow: hidden; 
            box-shadow: 0 10px 40px rgba(0,0,0,0.1); 
        }
        .header { 
            background: ${headerColors[type] || headerColors.general}; 
            color: white; 
            padding: 40px 30px; 
            text-align: center; 
        }
        .header h1 { 
            font-size: 32px; 
            font-weight: 300; 
            margin: 0 0 8px 0; 
            letter-spacing: 1px;
        }
        .header p { 
            font-size: 16px; 
            margin: 0; 
            opacity: 0.9; 
        }
        .content { 
            padding: 40px 30px; 
            font-size: 16px;
            line-height: 1.7;
        }
        .content h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .content p {
            margin-bottom: 16px;
        }
        .order-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .footer { 
            background: #f8f9fa; 
            padding: 30px; 
            text-align: center; 
            border-top: 1px solid #e9ecef; 
        }
        .footer p { 
            color: #6c757d; 
            font-size: 14px; 
            margin: 8px 0; 
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            .container { margin: 10px; border-radius: 8px; }
            .header, .content, .footer { padding: 20px; }
            .header h1 { font-size: 24px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${config.fromName}</h1>
            <p>Premium Skincare & Beauty</p>
        </div>
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>Thank you for choosing ${config.fromName}</p>
            <p>Need help? Contact us at <a href="mailto:${
              config.supportEmail
            }">${config.supportEmail}</a></p>
            <p>© 2024 ${config.fromName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;
};

// Log email to Firestore
const logEmail = async (emailData) => {
  try {
    await admin
      .firestore()
      .collection("email_logs")
      .add({
        ...emailData,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        createdAt: new Date(),
      });
  } catch (error) {
    console.error("Error logging email:", error);
  }
};

// Send email function
const sendEmail = async (emailData) => {
  const { to, subject, content, type = "general", orderReference } = emailData;
  const config = getEmailConfig();

  // Create HTML content
  const htmlContent = getEmailTemplate(content, subject, type);

  // Prepare email message
  const msg = {
    to,
    from: {
      email: config.fromEmail,
      name: config.fromName,
    },
    replyTo: config.replyTo,
    subject,
    html: htmlContent,
    text: content.replace(/<[^>]*>/g, ""), // Strip HTML for text version
  };

  // Log email data
  const logData = {
    to,
    subject,
    content: content.substring(0, 500) + (content.length > 500 ? "..." : ""),
    type,
    orderReference,
    status: "pending",
  };

  try {
    // Initialize SendGrid if not already done
    const isReady = initializeSendGrid();

    if (!isReady) {
      // Fallback to logging if SendGrid is not available
      logData.status = "logged_only";
      logData.provider = "none";
      logData.note = "SendGrid not configured - email logged only";
      logData.htmlContent = htmlContent.substring(0, 1000) + "...";
      console.log("SendGrid not available. Email logged only:", to, subject);
      await logEmail(logData);
      return logData;
    }

    // Send email via SendGrid
    await sgMail.send(msg);

    // Update log data for successful send
    logData.status = "sent";
    logData.provider = "sendgrid";
    logData.note = "Email sent successfully via SendGrid";
    console.log("Email sent successfully to:", to, "Subject:", subject);
  } catch (error) {
    console.error("Error sending email:", error);

    // Update log data for failed send
    logData.status = "failed";
    logData.provider = "sendgrid";
    logData.note = `SendGrid error: ${error.message}`;
    logData.error = error.message;
  }

  // Always log the email attempt
  await logEmail(logData);

  return logData;
};

// Predefined email templates
const emailTemplates = {
  orderConfirmation: (orderData) => {
    const { orderId, items, total, shipping } = orderData;
    const itemsList = items
      .map(
        (item) =>
          `<li>${item.name} x ${item.quantity} - $${(
            item.price * item.quantity
          ).toFixed(2)}</li>`
      )
      .join("");

    return {
      subject: `Order Confirmation - ${orderId}`,
      content: `
        <h2>Thank you for your order!</h2>
        <p>We've received your order and are preparing it for shipment.</p>
        
        <div class="order-details">
          <h3>Order Details</h3>
          <p><strong>Order ID:</strong> ${orderId}</p>
          <p><strong>Order Total:</strong> $${total.toFixed(2)}</p>
          
          <h4>Items Ordered:</h4>
          <ul>${itemsList}</ul>
          
          <h4>Shipping Address:</h4>
          <p>${shipping.name}<br>
          ${shipping.address}<br>
          ${shipping.city}, ${shipping.state} ${shipping.zipCode}</p>
        </div>
        
        <p>You'll receive a shipping confirmation email once your order is on its way.</p>
      `,
      type: "order",
    };
  },

  contactFormReply: (contactData) => {
    const { name, email, subject } = contactData;
    return {
      subject: `Thank you for contacting us - ${subject}`,
      content: `
        <h2>Thank you for reaching out!</h2>
        <p>Hi ${name},</p>
        <p>We've received your message and will get back to you within 24 hours.</p>
        <p>In the meantime, feel free to browse our latest products and skincare tips.</p>
        <p>Best regards,<br>The SkinGlow Team</p>
      `,
      type: "support",
    };
  },

  welcomeEmail: (userData) => {
    const { name, email } = userData;
    return {
      subject: "Welcome to SkinGlow!",
      content: `
        <h2>Welcome to SkinGlow, ${name}!</h2>
        <p>We're thrilled to have you join our community of skincare enthusiasts.</p>
        <p>As a new member, you'll enjoy:</p>
        <ul>
          <li>Exclusive access to new product launches</li>
          <li>Personalized skincare recommendations</li>
          <li>Special member-only discounts</li>
          <li>Expert skincare tips and tutorials</li>
        </ul>
        <p>Start exploring our premium collection today!</p>
        <a href="https://skinglow.com/shop" class="button">Shop Now</a>
      `,
      type: "welcome",
    };
  },
};

module.exports = {
  sendEmail,
  emailTemplates,
  getEmailTemplate,
  logEmail,
  initializeSendGrid,
};
